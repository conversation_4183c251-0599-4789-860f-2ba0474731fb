export interface Sach {
  id: number;
  tenSach: string;
  tacGia: string;
  nhaXuatBan: string;
  namXuatBan: number;
  isbn: string;
  soTrang: number;
  theLoai: string;
  soLuongTong: number;
  soLuongConLai: number;
  moTa?: string;
  hinhAnh?: string;
  gia?: number;
  ngayTao: Date;
  ngayCapNhat?: Date;
}

export interface CreateSachDto {
  tenSach: string;
  tacGia: string;
  nhaXuatBan: string;
  namXuatBan: number;
  isbn: string;
  soTrang: number;
  theLoai: string;
  soLuongTong: number;
  moTa?: string;
  hinhAnh?: string;
  gia?: number;
}

export interface UpdateSachDto {
  tenSach: string;
  tacGia: string;
  nhaXuatBan: string;
  namXuatBan: number;
  isbn: string;
  soTrang: number;
  theLoai: string;
  soLuongTong: number;
  moTa?: string;
  hinhAnh?: string;
  gia?: number;
}

export interface SachSearchDto {
  tenSach?: string;
  tacGia?: string;
  theLoai?: string;
  namXuatBanTu?: number;
  namXuatBanDen?: number;
  page: number;
  pageSize: number;
  sortBy?: string;
  sortDescending: boolean;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
