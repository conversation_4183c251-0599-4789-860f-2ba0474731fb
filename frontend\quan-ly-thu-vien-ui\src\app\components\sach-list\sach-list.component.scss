.sach-list-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-card {
  margin-bottom: 20px;

  mat-card-header {
    margin-bottom: 16px;
  }

  mat-card-actions {
    padding: 0 16px 16px;
  }
}

.search-card {
  margin-bottom: 20px;

  .search-row {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    margin-bottom: 16px;
    flex-wrap: wrap;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .search-field {
    flex: 1;
    min-width: 200px;
  }

  .search-buttons {
    display: flex;
    gap: 8px;
    align-items: center;

    button {
      white-space: nowrap;
    }
  }
}

.table-card {
  .table-container {
    position: relative;
    min-height: 400px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;

    mat-spinner {
      margin-bottom: 16px;
    }
  }

  .sach-table {
    width: 100%;

    .ten-sach-cell {
      max-width: 300px;

      .sach-info {
        strong {
          display: block;
          margin-bottom: 4px;
        }

        .mo-ta {
          color: rgba(0, 0, 0, 0.6);
          font-size: 12px;
          line-height: 1.3;
        }
      }
    }

    .the-loai-chip {
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .so-luong-available {
      color: #4caf50;
      font-weight: 500;
    }

    .so-luong-unavailable {
      color: #f44336;
      font-weight: 500;
    }
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: rgba(0, 0, 0, 0.6);

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .sach-list-container {
    padding: 10px;
  }

  .search-row {
    flex-direction: column;

    .search-field {
      width: 100%;
    }

    .search-buttons {
      width: 100%;
      justify-content: center;
    }
  }

  .sach-table {
    .ten-sach-cell {
      max-width: 200px;
    }
  }
}