import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snackbar';

import { SachService } from '../../services/sach.service';
import { Sach, CreateSachDto, UpdateSachDto } from '../../models/sach.model';

export interface SachFormData {
  mode: 'create' | 'edit';
  sach?: Sach;
}

@Component({
  selector: 'app-sach-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule
  ],
  templateUrl: './sach-form.component.html',
  styleUrl: './sach-form.component.scss'
})
export class SachFormComponent implements OnInit {
  sachForm: FormGroup;
  isEditMode: boolean;
  isSubmitting = false;

  theLoaiOptions = [
    'Công nghệ thông tin',
    'Cơ sở dữ liệu',
    'Kỹ thuật phần mềm',
    'Văn học',
    'Khoa học',
    'Lịch sử',
    'Triết học'
  ];

  constructor(
    private fb: FormBuilder,
    private sachService: SachService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<SachFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SachFormData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.sachForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.isEditMode && this.data.sach) {
      this.populateForm(this.data.sach);
    }
  }

  createForm(): FormGroup {
    return this.fb.group({
      tenSach: ['', [Validators.required, Validators.maxLength(200)]],
      tacGia: ['', [Validators.required, Validators.maxLength(100)]],
      nhaXuatBan: ['', [Validators.required, Validators.maxLength(50)]],
      namXuatBan: ['', [Validators.required, Validators.min(1000), Validators.max(new Date().getFullYear())]],
      isbn: ['', [Validators.required, Validators.maxLength(20)]],
      soTrang: ['', [Validators.required, Validators.min(1)]],
      theLoai: ['', [Validators.required, Validators.maxLength(50)]],
      soLuongTong: ['', [Validators.required, Validators.min(1)]],
      moTa: ['', [Validators.maxLength(500)]],
      hinhAnh: [''],
      gia: ['', [Validators.min(0)]]
    });
  }

  populateForm(sach: Sach): void {
    this.sachForm.patchValue({
      tenSach: sach.tenSach,
      tacGia: sach.tacGia,
      nhaXuatBan: sach.nhaXuatBan,
      namXuatBan: sach.namXuatBan,
      isbn: sach.isbn,
      soTrang: sach.soTrang,
      theLoai: sach.theLoai,
      soLuongTong: sach.soLuongTong,
      moTa: sach.moTa,
      hinhAnh: sach.hinhAnh,
      gia: sach.gia
    });
  }

  onSubmit(): void {
    if (this.sachForm.valid) {
      this.isSubmitting = true;
      const formValue = this.sachForm.value;

      if (this.isEditMode) {
        const updateDto: UpdateSachDto = formValue;
        this.sachService.update(this.data.sach!.id, updateDto).subscribe({
          next: (result) => {
            this.dialogRef.close(result);
          },
          error: (error) => {
            console.error('Error updating sách:', error);
            this.snackBar.open('Lỗi khi cập nhật sách', 'Đóng', { duration: 3000 });
            this.isSubmitting = false;
          }
        });
      } else {
        const createDto: CreateSachDto = formValue;
        this.sachService.create(createDto).subscribe({
          next: (result) => {
            this.dialogRef.close(result);
          },
          error: (error) => {
            console.error('Error creating sách:', error);
            this.snackBar.open('Lỗi khi thêm sách', 'Đóng', { duration: 3000 });
            this.isSubmitting = false;
          }
        });
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private markFormGroupTouched(): void {
    Object.keys(this.sachForm.controls).forEach(key => {
      const control = this.sachForm.get(key);
      control?.markAsTouched();
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.sachForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${this.getFieldDisplayName(fieldName)} là bắt buộc`;
    }
    if (control?.hasError('maxlength')) {
      const maxLength = control.errors?.['maxlength'].requiredLength;
      return `${this.getFieldDisplayName(fieldName)} không được vượt quá ${maxLength} ký tự`;
    }
    if (control?.hasError('min')) {
      const min = control.errors?.['min'].min;
      return `${this.getFieldDisplayName(fieldName)} phải lớn hơn hoặc bằng ${min}`;
    }
    if (control?.hasError('max')) {
      const max = control.errors?.['max'].max;
      return `${this.getFieldDisplayName(fieldName)} phải nhỏ hơn hoặc bằng ${max}`;
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      tenSach: 'Tên sách',
      tacGia: 'Tác giả',
      nhaXuatBan: 'Nhà xuất bản',
      namXuatBan: 'Năm xuất bản',
      isbn: 'ISBN',
      soTrang: 'Số trang',
      theLoai: 'Thể loại',
      soLuongTong: 'Số lượng',
      moTa: 'Mô tả',
      hinhAnh: 'Hình ảnh',
      gia: 'Giá'
    };
    return fieldNames[fieldName] || fieldName;
  }
}
