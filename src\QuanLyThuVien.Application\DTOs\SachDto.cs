namespace QuanLyThuVien.Application.DTOs;

public class SachDto
{
    public int Id { get; set; }
    public string TenSach { get; set; } = string.Empty;
    public string TacGia { get; set; } = string.Empty;
    public string NhaXuatBan { get; set; } = string.Empty;
    public int NamXuatBan { get; set; }
    public string ISBN { get; set; } = string.Empty;
    public int SoTrang { get; set; }
    public string TheLoai { get; set; } = string.Empty;
    public int SoLuongTong { get; set; }
    public int SoLuongConLai { get; set; }
    public string? MoTa { get; set; }
    public string? HinhAnh { get; set; }
    public decimal? Gia { get; set; }
    public DateTime NgayTao { get; set; }
    public DateTime? NgayCapNhat { get; set; }
}

public class CreateSachDto
{
    public string TenSach { get; set; } = string.Empty;
    public string TacGia { get; set; } = string.Empty;
    public string NhaXuatBan { get; set; } = string.Empty;
    public int NamXuatBan { get; set; }
    public string ISBN { get; set; } = string.Empty;
    public int SoTrang { get; set; }
    public string TheLoai { get; set; } = string.Empty;
    public int SoLuongTong { get; set; }
    public string? MoTa { get; set; }
    public string? HinhAnh { get; set; }
    public decimal? Gia { get; set; }
}

public class UpdateSachDto
{
    public string TenSach { get; set; } = string.Empty;
    public string TacGia { get; set; } = string.Empty;
    public string NhaXuatBan { get; set; } = string.Empty;
    public int NamXuatBan { get; set; }
    public string ISBN { get; set; } = string.Empty;
    public int SoTrang { get; set; }
    public string TheLoai { get; set; } = string.Empty;
    public int SoLuongTong { get; set; }
    public string? MoTa { get; set; }
    public string? HinhAnh { get; set; }
    public decimal? Gia { get; set; }
}

public class SachSearchDto
{
    public string? TenSach { get; set; }
    public string? TacGia { get; set; }
    public string? TheLoai { get; set; }
    public int? NamXuatBanTu { get; set; }
    public int? NamXuatBanDen { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SortBy { get; set; } = "TenSach";
    public bool SortDescending { get; set; } = false;
}

public class PagedResult<T>
{
    public List<T> Items { get; set; } = new();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    public bool HasNextPage => Page < TotalPages;
    public bool HasPreviousPage => Page > 1;
}
