.sach-form-container {
  min-width: 500px;
  max-width: 600px;

  h2[mat-dialog-title] {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 0;

    mat-icon {
      color: #1976d2;
    }
  }

  mat-dialog-content {
    padding: 20px 0;
    max-height: 70vh;
    overflow-y: auto;

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .full-width {
      width: 100%;
    }

    .half-width {
      flex: 1;
      min-width: 0;
    }

    .third-width {
      flex: 1;
      min-width: 0;
    }

    mat-form-field {
      mat-error {
        font-size: 12px;
      }
    }
  }

  mat-dialog-actions {
    padding: 16px 0 0;
    margin: 0;

    button {
      margin-left: 8px;

      &:first-child {
        margin-left: 0;
      }

      mat-icon {
        margin-right: 4px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .sach-form-container {
    min-width: 300px;
    max-width: 90vw;

    mat-dialog-content {
      .form-row {
        flex-direction: column;
        gap: 8px;

        .half-width,
        .third-width {
          width: 100%;
        }
      }
    }

    mat-dialog-actions {
      flex-direction: column-reverse;

      button {
        width: 100%;
        margin: 4px 0;
      }
    }
  }
}