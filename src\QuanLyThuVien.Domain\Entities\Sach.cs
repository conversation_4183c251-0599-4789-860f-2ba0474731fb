using System.ComponentModel.DataAnnotations;

namespace QuanLyThuVien.Domain.Entities;

public class Sach : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string TenSach { get; set; } = string.Empty;

    [MaxLength(100)]
    public string <PERSON>c<PERSON><PERSON> { get; set; } = string.Empty;

    [MaxLength(50)]
    public string NhaXuatBan { get; set; } = string.Empty;

    public int NamXuatBan { get; set; }

    [MaxLength(20)]
    public string ISBN { get; set; } = string.Empty;

    public int SoTrang { get; set; }

    [MaxLength(50)]
    public string TheLoai { get; set; } = string.Empty;

    public int SoLuongTong { get; set; }

    public int SoLuongConLai { get; set; }

    [MaxLength(500)]
    public string? MoTa { get; set; }

    public string? HinhAnh { get; set; }

    public decimal? Gia { get; set; }

    // Navigation properties
    public virtual ICollection<MuonTraSach> DanhSachMuonTra { get; set; } = new List<MuonTraSach>();
}
