<div class="sach-form-container">
  <h2 mat-dialog-title>
    <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
    {{ isEditMode ? 'Chỉnh sửa sách' : 'Thêm sách mới' }}
  </h2>

  <mat-dialog-content>
    <form [formGroup]="sachForm" (ngSubmit)="onSubmit()">
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Tên sách *</mat-label>
          <input matInput formControlName="tenSach" placeholder="Nhập tên sách">
          <mat-error *ngIf="sachForm.get('tenSach')?.invalid && sachForm.get('tenSach')?.touched">
            {{ getErrorMessage('tenSach') }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Tác gi<PERSON> *</mat-label>
          <input matInput formControlName="tacGia" placeholder="Nhập tên tác giả">
          <mat-error *ngIf="sachForm.get('tacGia')?.invalid && sachForm.get('tacGia')?.touched">
            {{ getErrorMessage('tacGia') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Nhà xuất bản *</mat-label>
          <input matInput formControlName="nhaXuatBan" placeholder="Nhập nhà xuất bản">
          <mat-error *ngIf="sachForm.get('nhaXuatBan')?.invalid && sachForm.get('nhaXuatBan')?.touched">
            {{ getErrorMessage('nhaXuatBan') }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Năm xuất bản *</mat-label>
          <input matInput type="number" formControlName="namXuatBan" placeholder="Nhập năm xuất bản">
          <mat-error *ngIf="sachForm.get('namXuatBan')?.invalid && sachForm.get('namXuatBan')?.touched">
            {{ getErrorMessage('namXuatBan') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>ISBN *</mat-label>
          <input matInput formControlName="isbn" placeholder="Nhập mã ISBN">
          <mat-error *ngIf="sachForm.get('isbn')?.invalid && sachForm.get('isbn')?.touched">
            {{ getErrorMessage('isbn') }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="third-width">
          <mat-label>Số trang *</mat-label>
          <input matInput type="number" formControlName="soTrang" placeholder="Số trang">
          <mat-error *ngIf="sachForm.get('soTrang')?.invalid && sachForm.get('soTrang')?.touched">
            {{ getErrorMessage('soTrang') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="third-width">
          <mat-label>Thể loại *</mat-label>
          <mat-select formControlName="theLoai">
            <mat-option *ngFor="let theLoai of theLoaiOptions" [value]="theLoai">
              {{ theLoai }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="sachForm.get('theLoai')?.invalid && sachForm.get('theLoai')?.touched">
            {{ getErrorMessage('theLoai') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="third-width">
          <mat-label>Số lượng *</mat-label>
          <input matInput type="number" formControlName="soLuongTong" placeholder="Số lượng">
          <mat-error *ngIf="sachForm.get('soLuongTong')?.invalid && sachForm.get('soLuongTong')?.touched">
            {{ getErrorMessage('soLuongTong') }}
          </mat-error>
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Giá (VNĐ)</mat-label>
          <input matInput type="number" formControlName="gia" placeholder="Nhập giá sách">
          <mat-error *ngIf="sachForm.get('gia')?.invalid && sachForm.get('gia')?.touched">
            {{ getErrorMessage('gia') }}
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="half-width">
          <mat-label>Hình ảnh (URL)</mat-label>
          <input matInput formControlName="hinhAnh" placeholder="Nhập URL hình ảnh">
        </mat-form-field>
      </div>

      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Mô tả</mat-label>
          <textarea matInput formControlName="moTa" rows="4" placeholder="Nhập mô tả sách"></textarea>
          <mat-error *ngIf="sachForm.get('moTa')?.invalid && sachForm.get('moTa')?.touched">
            {{ getErrorMessage('moTa') }}
          </mat-error>
        </mat-form-field>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">
      Hủy
    </button>
    <button mat-raised-button color="primary" type="button" (click)="onSubmit()" [disabled]="isSubmitting || sachForm.invalid">
      <mat-icon *ngIf="isSubmitting">hourglass_empty</mat-icon>
      <mat-icon *ngIf="!isSubmitting">{{ isEditMode ? 'save' : 'add' }}</mat-icon>
      {{ isSubmitting ? 'Đang xử lý...' : (isEditMode ? 'Cập nhật' : 'Thêm mới') }}
    </button>
  </mat-dialog-actions>
</div>
