<div class="sach-list-container">
  <!-- Header -->
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title><PERSON><PERSON><PERSON><PERSON> <PERSON></mat-card-title>
      <mat-card-subtitle><PERSON><PERSON> sách tất cả sách trong thư viện</mat-card-subtitle>
    </mat-card-header>
    <mat-card-actions>
      <button mat-raised-button color="primary" (click)="openCreateDialog()">
        <mat-icon>add</mat-icon>
        Thêm sách mới
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Search Form -->
  <mat-card class="search-card">
    <mat-card-content>
      <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
        <div class="search-row">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Tên sách</mat-label>
            <input matInput formControlName="tenSach" placeholder="Nhập tên sách">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Tác gi<PERSON></mat-label>
            <input matInput formControlName="tacGia" placeholder="Nhập tên tác giả">
            <mat-icon matSuffix>person</mat-icon>
          </mat-form-field>

          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Thể loại</mat-label>
            <mat-select formControlName="theLoai">
              <mat-option value="">Tất cả</mat-option>
              <mat-option *ngFor="let theLoai of theLoaiOptions" [value]="theLoai">
                {{theLoai}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="search-row">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Năm xuất bản từ</mat-label>
            <input matInput type="number" formControlName="namXuatBanTu" placeholder="Từ năm">
          </mat-form-field>

          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Năm xuất bản đến</mat-label>
            <input matInput type="number" formControlName="namXuatBanDen" placeholder="Đến năm">
          </mat-form-field>

          <div class="search-buttons">
            <button mat-raised-button color="primary" type="submit">
              <mat-icon>search</mat-icon>
              Tìm kiếm
            </button>
            <button mat-button type="button" (click)="onClearSearch()">
              <mat-icon>clear</mat-icon>
              Xóa bộ lọc
            </button>
          </div>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Data Table -->
  <mat-card class="table-card">
    <mat-card-content>
      <div class="table-container">
        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner></mat-spinner>
          <p>Đang tải dữ liệu...</p>
        </div>

        <table mat-table [dataSource]="dataSource" class="sach-table" *ngIf="!isLoading">
          <!-- Tên sách Column -->
          <ng-container matColumnDef="tenSach">
            <th mat-header-cell *matHeaderCellDef>Tên sách</th>
            <td mat-cell *matCellDef="let sach" class="ten-sach-cell">
              <div class="sach-info">
                <strong>{{sach.tenSach}}</strong>
                <small *ngIf="sach.moTa" class="mo-ta">{{sach.moTa | slice:0:100}}{{sach.moTa.length > 100 ? '...' : ''}}</small>
              </div>
            </td>
          </ng-container>

          <!-- Tác giả Column -->
          <ng-container matColumnDef="tacGia">
            <th mat-header-cell *matHeaderCellDef>Tác giả</th>
            <td mat-cell *matCellDef="let sach">{{sach.tacGia}}</td>
          </ng-container>

          <!-- Nhà xuất bản Column -->
          <ng-container matColumnDef="nhaXuatBan">
            <th mat-header-cell *matHeaderCellDef>Nhà xuất bản</th>
            <td mat-cell *matCellDef="let sach">{{sach.nhaXuatBan}}</td>
          </ng-container>

          <!-- Năm xuất bản Column -->
          <ng-container matColumnDef="namXuatBan">
            <th mat-header-cell *matHeaderCellDef>Năm XB</th>
            <td mat-cell *matCellDef="let sach">{{sach.namXuatBan}}</td>
          </ng-container>

          <!-- Thể loại Column -->
          <ng-container matColumnDef="theLoai">
            <th mat-header-cell *matHeaderCellDef>Thể loại</th>
            <td mat-cell *matCellDef="let sach">
              <span class="the-loai-chip">{{sach.theLoai}}</span>
            </td>
          </ng-container>

          <!-- Số lượng còn lại Column -->
          <ng-container matColumnDef="soLuongConLai">
            <th mat-header-cell *matHeaderCellDef>Còn lại</th>
            <td mat-cell *matCellDef="let sach">
              <span [class]="sach.soLuongConLai > 0 ? 'so-luong-available' : 'so-luong-unavailable'">
                {{sach.soLuongConLai}}/{{sach.soLuongTong}}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Thao tác</th>
            <td mat-cell *matCellDef="let sach">
              <button mat-icon-button color="primary" (click)="openEditDialog(sach)" matTooltip="Chỉnh sửa">
                <mat-icon>edit</mat-icon>
              </button>
              <button mat-icon-button color="warn" (click)="deleteSach(sach)" matTooltip="Xóa">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <!-- No data message -->
        <div *ngIf="!isLoading && dataSource.length === 0" class="no-data">
          <mat-icon>book</mat-icon>
          <p>Không có sách nào được tìm thấy</p>
        </div>
      </div>

      <!-- Paginator -->
      <mat-paginator
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>
