import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snackbar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { SachService } from '../../services/sach.service';
import { Sach, SachSearchDto, PagedResult } from '../../models/sach.model';
import { SachFormComponent } from '../sach-form/sach-form.component';

@Component({
  selector: 'app-sach-list',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatSelectModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './sach-list.component.html',
  styleUrl: './sach-list.component.scss'
})
export class SachListComponent implements OnInit {
  displayedColumns: string[] = ['tenSach', 'tacGia', 'nhaXuatBan', 'namXuatBan', 'theLoai', 'soLuongConLai', 'actions'];
  dataSource: Sach[] = [];
  totalCount = 0;
  pageSize = 10;
  pageIndex = 0;
  isLoading = false;

  searchForm: FormGroup;

  theLoaiOptions = [
    'Công nghệ thông tin',
    'Cơ sở dữ liệu',
    'Kỹ thuật phần mềm',
    'Văn học',
    'Khoa học',
    'Lịch sử',
    'Triết học'
  ];

  constructor(
    private sachService: SachService,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.searchForm = this.fb.group({
      tenSach: [''],
      tacGia: [''],
      theLoai: [''],
      namXuatBanTu: [''],
      namXuatBanDen: ['']
    });
  }

  ngOnInit(): void {
    this.loadSach();
  }

  loadSach(): void {
    this.isLoading = true;
    const searchDto: SachSearchDto = {
      ...this.searchForm.value,
      page: this.pageIndex + 1,
      pageSize: this.pageSize,
      sortBy: 'tenSach',
      sortDescending: false
    };

    this.sachService.getAll(searchDto).subscribe({
      next: (result: PagedResult<Sach>) => {
        this.dataSource = result.items;
        this.totalCount = result.totalCount;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading sách:', error);
        this.snackBar.open('Lỗi khi tải danh sách sách', 'Đóng', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  onSearch(): void {
    this.pageIndex = 0;
    this.loadSach();
  }

  onClearSearch(): void {
    this.searchForm.reset();
    this.pageIndex = 0;
    this.loadSach();
  }

  onPageChange(event: PageEvent): void {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadSach();
  }

  openCreateDialog(): void {
    const dialogRef = this.dialog.open(SachFormComponent, {
      width: '600px',
      data: { mode: 'create' }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSach();
        this.snackBar.open('Thêm sách thành công', 'Đóng', { duration: 3000 });
      }
    });
  }

  openEditDialog(sach: Sach): void {
    const dialogRef = this.dialog.open(SachFormComponent, {
      width: '600px',
      data: { mode: 'edit', sach: sach }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadSach();
        this.snackBar.open('Cập nhật sách thành công', 'Đóng', { duration: 3000 });
      }
    });
  }

  deleteSach(sach: Sach): void {
    if (confirm(`Bạn có chắc chắn muốn xóa sách "${sach.tenSach}"?`)) {
      this.sachService.delete(sach.id).subscribe({
        next: () => {
          this.loadSach();
          this.snackBar.open('Xóa sách thành công', 'Đóng', { duration: 3000 });
        },
        error: (error) => {
          console.error('Error deleting sách:', error);
          this.snackBar.open('Lỗi khi xóa sách', 'Đóng', { duration: 3000 });
        }
      });
    }
  }
}
