using System.ComponentModel.DataAnnotations;

namespace QuanLyThuVien.Domain.Entities;

public class MuonTraSach : BaseEntity
{
    public int NguoiDungId { get; set; }
    public int SachId { get; set; }

    public DateTime NgayMuon { get; set; } = DateTime.Now;

    public DateTime NgayHenTra { get; set; }

    public DateTime? NgayTraThucTe { get; set; }

    [MaxLength(20)]
    public string TrangThai { get; set; } = "DangMuon"; // DangMuon, DaTra, QuaHan

    [MaxLength(500)]
    public string? GhiChu { get; set; }

    public decimal? TienPhat { get; set; }

    // Navigation properties
    public virtual NguoiDung NguoiDung { get; set; } = null!;
    public virtual Sach Sach { get; set; } = null!;
}
