using Microsoft.AspNetCore.Mvc;
using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;

namespace QuanLyThuVien.WebAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SachController : ControllerBase
{
    private readonly ISachService _sachService;

    public SachController(ISachService sachService)
    {
        _sachService = sachService;
    }

    /// <summary>
    /// Lấy danh sách sách với tìm kiếm và phân trang
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<PagedResult<SachDto>>> GetAll([FromQuery] SachSearchDto searchDto)
    {
        try
        {
            var result = await _sachService.GetAllAsync(searchDto);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// <PERSON><PERSON><PERSON> thông tin sách theo ID
    /// </summary>
    [HttpGet("{id}")]
    public async Task<ActionResult<SachDto>> GetById(int id)
    {
        try
        {
            var sach = await _sachService.GetByIdAsync(id);
            if (sach == null)
            {
                return NotFound(new { message = $"Không tìm thấy sách với ID: {id}" });
            }
            return Ok(sach);
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Tạo sách mới
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<SachDto>> Create([FromBody] CreateSachDto createDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var sach = await _sachService.CreateAsync(createDto);
            return CreatedAtAction(nameof(GetById), new { id = sach.Id }, sach);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Cập nhật thông tin sách
    /// </summary>
    [HttpPut("{id}")]
    public async Task<ActionResult<SachDto>> Update(int id, [FromBody] UpdateSachDto updateDto)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var sach = await _sachService.UpdateAsync(id, updateDto);
            return Ok(sach);
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }

    /// <summary>
    /// Xóa sách (soft delete)
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult> Delete(int id)
    {
        try
        {
            var result = await _sachService.DeleteAsync(id);
            if (!result)
            {
                return NotFound(new { message = $"Không tìm thấy sách với ID: {id}" });
            }
            return Ok(new { message = "Xóa sách thành công" });
        }
        catch (KeyNotFoundException ex)
        {
            return NotFound(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new { message = "Lỗi server", error = ex.Message });
        }
    }
}
