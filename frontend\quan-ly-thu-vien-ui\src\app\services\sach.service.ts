import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Sach, CreateSachDto, UpdateSachDto, SachSearchDto, PagedResult } from '../models/sach.model';

@Injectable({
  providedIn: 'root'
})
export class SachService {
  private apiUrl = 'http://localhost:5247/api/sach';

  constructor(private http: HttpClient) { }

  getAll(searchDto: SachSearchDto): Observable<PagedResult<Sach>> {
    let params = new HttpParams()
      .set('page', searchDto.page.toString())
      .set('pageSize', searchDto.pageSize.toString())
      .set('sortDescending', searchDto.sortDescending.toString());

    if (searchDto.tenSach) {
      params = params.set('tenSach', searchDto.tenSach);
    }
    if (searchDto.tacGia) {
      params = params.set('tacGia', searchDto.tacGia);
    }
    if (searchDto.theLoai) {
      params = params.set('theLoai', searchDto.theLoai);
    }
    if (searchDto.namXuatBanTu) {
      params = params.set('namXuatBanTu', searchDto.namXuatBanTu.toString());
    }
    if (searchDto.namXuatBanDen) {
      params = params.set('namXuatBanDen', searchDto.namXuatBanDen.toString());
    }
    if (searchDto.sortBy) {
      params = params.set('sortBy', searchDto.sortBy);
    }

    return this.http.get<PagedResult<Sach>>(this.apiUrl, { params });
  }

  getById(id: number): Observable<Sach> {
    return this.http.get<Sach>(`${this.apiUrl}/${id}`);
  }

  create(sach: CreateSachDto): Observable<Sach> {
    return this.http.post<Sach>(this.apiUrl, sach);
  }

  update(id: number, sach: UpdateSachDto): Observable<Sach> {
    return this.http.put<Sach>(`${this.apiUrl}/${id}`, sach);
  }

  delete(id: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/${id}`);
  }
}
