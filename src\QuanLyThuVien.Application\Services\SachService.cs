using QuanLyThuVien.Application.DTOs;
using QuanLyThuVien.Application.Interfaces;
using QuanLyThuVien.Domain.Entities;

namespace QuanLyThuVien.Application.Services;

public class SachService : ISachService
{
    private readonly ISachRepository _sachRepository;

    public SachService(ISachRepository sachRepository)
    {
        _sachRepository = sachRepository;
    }

    public async Task<PagedResult<SachDto>> GetAllAsync(SachSearchDto searchDto)
    {
        var result = await _sachRepository.GetAllAsync(searchDto);
        
        return new PagedResult<SachDto>
        {
            Items = result.Items.Select(MapToDto).ToList(),
            TotalCount = result.TotalCount,
            Page = result.Page,
            PageSize = result.PageSize
        };
    }

    public async Task<SachDto?> GetByIdAsync(int id)
    {
        var sach = await _sachRepository.GetByIdAsync(id);
        return sach != null ? MapToDto(sach) : null;
    }

    public async Task<SachDto> CreateAsync(CreateSachDto createDto)
    {
        // Validate ISBN uniqueness
        if (!string.IsNullOrEmpty(createDto.ISBN) && await _sachRepository.IsISBNExistsAsync(createDto.ISBN))
        {
            throw new InvalidOperationException($"ISBN '{createDto.ISBN}' đã tồn tại trong hệ thống.");
        }

        var sach = new Sach
        {
            TenSach = createDto.TenSach,
            TacGia = createDto.TacGia,
            NhaXuatBan = createDto.NhaXuatBan,
            NamXuatBan = createDto.NamXuatBan,
            ISBN = createDto.ISBN,
            SoTrang = createDto.SoTrang,
            TheLoai = createDto.TheLoai,
            SoLuongTong = createDto.SoLuongTong,
            MoTa = createDto.MoTa,
            HinhAnh = createDto.HinhAnh,
            Gia = createDto.Gia
        };

        var createdSach = await _sachRepository.CreateAsync(sach);
        return MapToDto(createdSach);
    }

    public async Task<SachDto> UpdateAsync(int id, UpdateSachDto updateDto)
    {
        var existingSach = await _sachRepository.GetByIdAsync(id);
        if (existingSach == null)
        {
            throw new KeyNotFoundException($"Không tìm thấy sách với ID: {id}");
        }

        // Validate ISBN uniqueness (excluding current record)
        if (!string.IsNullOrEmpty(updateDto.ISBN) && await _sachRepository.IsISBNExistsAsync(updateDto.ISBN, id))
        {
            throw new InvalidOperationException($"ISBN '{updateDto.ISBN}' đã tồn tại trong hệ thống.");
        }

        // Update properties
        existingSach.TenSach = updateDto.TenSach;
        existingSach.TacGia = updateDto.TacGia;
        existingSach.NhaXuatBan = updateDto.NhaXuatBan;
        existingSach.NamXuatBan = updateDto.NamXuatBan;
        existingSach.ISBN = updateDto.ISBN;
        existingSach.SoTrang = updateDto.SoTrang;
        existingSach.TheLoai = updateDto.TheLoai;
        
        // Update quantity logic
        var soLuongDiff = updateDto.SoLuongTong - existingSach.SoLuongTong;
        existingSach.SoLuongTong = updateDto.SoLuongTong;
        existingSach.SoLuongConLai += soLuongDiff;
        
        existingSach.MoTa = updateDto.MoTa;
        existingSach.HinhAnh = updateDto.HinhAnh;
        existingSach.Gia = updateDto.Gia;

        var updatedSach = await _sachRepository.UpdateAsync(existingSach);
        return MapToDto(updatedSach);
    }

    public async Task<bool> DeleteAsync(int id)
    {
        if (!await _sachRepository.ExistsAsync(id))
        {
            throw new KeyNotFoundException($"Không tìm thấy sách với ID: {id}");
        }

        return await _sachRepository.DeleteAsync(id);
    }

    private static SachDto MapToDto(Sach sach)
    {
        return new SachDto
        {
            Id = sach.Id,
            TenSach = sach.TenSach,
            TacGia = sach.TacGia,
            NhaXuatBan = sach.NhaXuatBan,
            NamXuatBan = sach.NamXuatBan,
            ISBN = sach.ISBN,
            SoTrang = sach.SoTrang,
            TheLoai = sach.TheLoai,
            SoLuongTong = sach.SoLuongTong,
            SoLuongConLai = sach.SoLuongConLai,
            MoTa = sach.MoTa,
            HinhAnh = sach.HinhAnh,
            Gia = sach.Gia,
            NgayTao = sach.NgayTao,
            NgayCapNhat = sach.NgayCapNhat
        };
    }
}
